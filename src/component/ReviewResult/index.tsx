import React, { useState, useEffect } from 'react'
import { But<PERSON>, Card, Tag, Steps, Input, message } from 'antd'
import { CheckCircleFilled, EditOutlined } from '@ant-design/icons'
import './index.less'
import StreamTypewriter from '../StreamTypewriter'

export interface ReviewResultProps {
  type: string //   "scene" | "unit" // 组件类型：场景还是单元场景
  // 标题
  title?: string
  // 评审结果
  reviewResult?: boolean
  // 步骤数据（用于场景规划）
  steps?: any[]
  reviewContent?: any // 评审内容
  // 是否显示“场景规划”区域（评审先显示，规划生成后再显示）
  showScenario?: boolean
  onContinue?: () => void
  onConfirm?: () => void
  onBackIndex?: () => void
  onConfirmScenario?: (steps: any[]) => void
  sceneStream?: string // 场景评审流式数据
}

const ReviewResult: React.FC<ReviewResultProps> = ({
  type = 'unit',
  title,
  reviewResult,
  reviewContent,
  showScenario,
  steps = [],
  onBackIndex,
  onContinue,
  onConfirm,
  onConfirmScenario,
  sceneStream,
}) => {
  // 编辑状态管理
  const [editingStep, setEditingStep] = useState<number | null>(null)
  const [editingData, setEditingData] = useState<{
    title: string
    inputDesc: string
    outputDesc: string
  }>({
    title: '',
    inputDesc: '',
    outputDesc: '',
  })

  // 本地步骤数据，保存编辑结果用于刷新 UI
  const [localSteps, setLocalSteps] = useState<any[]>(steps || [])
  const scrollRef = React.useRef<HTMLDivElement>(null)


  // 当外部 steps 变化时，同步到本地
  useEffect(() => {
    setLocalSteps(steps || [])
  }, [steps])

  // 开始编辑步骤
  const handleStartEdit = (stepIndex: number, step: any) => {
    setEditingStep(stepIndex)
    setEditingData({
      title: step.title || '',
      inputDesc: step.inputDesc || '',
      outputDesc: step.outputDesc || '',
    })
  }

  // 保存编辑
  const handleSaveEdit = (stepIndex: number) => {
    if (!editingData.title.trim()) {
      message.warning('步骤标题不能为空')
      return
    }

    // 写回到本地步骤数据
    setLocalSteps((prev) => {
      const next = [...prev]
      const oldStep = next[stepIndex] || {}
      next[stepIndex] = {
        ...oldStep,
        title: editingData.title,
        inputDesc: editingData.inputDesc,
        outputDesc: editingData.outputDesc,
      }
      return next
    })

    // 退出编辑状态
    setEditingStep(null)
    message.success('保存成功')
  }

  // 取消编辑
  const handleCancelEdit = () => {
    setEditingStep(null)
  }

  return (
    <div className="review-result-container">
      {reviewContent ? (
        <div>
          <div className="review-result">
            <div className="review-header">
              <h3 className="review-title">{title || '场景输入评审'}</h3>
            </div>

            <div className="review-steps">
              <Steps
                direction="vertical"
                size="small"
                current={1}
                items={[
                  {
                    title: '开始评审任务',
                    // description: '0.02ms',
                    status: 'finish',
                    icon: <CheckCircleFilled className="step-icon finish" />,
                  },
                  {
                    title: '完成评审任务',
                    // description: '2s',
                    status: 'finish',
                    icon: <CheckCircleFilled className="step-icon finish" />,
                  },
                ]}
              />
            </div>
            <Card className="result-card">
              <div className="result-content">
                <div className="result-row">
                  <span className="result-label">评审结果:</span>
                  <Tag
                    color={reviewResult ? 'success' : 'error'}
                    className="result-tag"
                  >
                    {reviewResult ? '通过' : '异常'}
                  </Tag>
                </div>

                <div className="result-row">
                  <span className="result-label">评审描述:</span>
                  <span className="result-description">
                    {reviewContent['描述'] || ''}
                  </span>
                </div>
              </div>
            </Card>

            <div className="review-actions">
              {/* {type == 'unit' && (
                <Button type="primary" onClick={onContinue}>
                  确定
                </Button>
              )} */}

              {!reviewResult && (
                <>
                  <Button type="primary" onClick={onBackIndex}>
                    返回首页
                  </Button>
                  <Button type="link" onClick={onContinue}>
                    忽略错误，继续下一步
                  </Button>
                </>
              )}
            </div>
          </div>

          {/* 场景规划 */}
          {type === 'scene' && showScenario && (
            <div className="scenario-planning">
              <div className="scenario-header">
                <h2 className="scenario-title">场景规划</h2>
              </div>
              {localSteps.length > 0 ? (
                <>
                  <div className="scenario-content">
                    {localSteps.map((step, index) => (
                      <div key={index} className="scenario-step">
                        <div className="step-header">
                          <div className="step-title-section">
                            {editingStep === index ? (
                              <Input
                                value={editingData.title}
                                onChange={(e) =>
                                  setEditingData((prev) => ({
                                    ...prev,
                                    title: e.target.value,
                                  }))
                                }
                                className="editing-title"
                                placeholder="请输入步骤标题"
                              />
                            ) : (
                              <h3 className="step-title">{step.title}</h3>
                            )}
                            <Button size="small" className="unit-scene-button">
                              单元场景
                            </Button>
                          </div>
                          {step.role && (
                            <span className="step-role">@{step.role}</span>
                          )}
                        </div>

                        <div className="step-content">
                          <div className="step-input">
                            <strong>输入:</strong>
                            {editingStep === index ? (
                              <Input
                                value={editingData.inputDesc}
                                onChange={(e) =>
                                  setEditingData((prev) => ({
                                    ...prev,
                                    inputDesc: e.target.value,
                                  }))
                                }
                                className="editing-input"
                                placeholder="请输入输入内容"
                              />
                            ) : (
                              <span>{step.inputDesc}</span>
                            )}
                          </div>
                          <div className="step-output">
                            <strong>输出:</strong>
                            {editingStep === index ? (
                              <Input
                                value={editingData.outputDesc}
                                onChange={(e) =>
                                  setEditingData((prev) => ({
                                    ...prev,
                                    outputDesc: e.target.value,
                                  }))
                                }
                                className="editing-output"
                                placeholder="请输入输出内容"
                              />
                            ) : (
                              <span>{step.outputDesc}</span>
                            )}
                          </div>

                          {/* 编辑按钮放在右下角 */}
                          <div className="step-edit-section">
                            {editingStep === index ? (
                              <div className="edit-actions">
                                <Button
                                  size="small"
                                  type="primary"
                                  className="save-button"
                                  onClick={() => handleSaveEdit(index)}
                                >
                                  保存
                                </Button>
                                <Button
                                  size="small"
                                  className="cancel-button"
                                  onClick={handleCancelEdit}
                                >
                                  取消
                                </Button>
                              </div>
                            ) : (
                              <Button
                                size="small"
                                type="text"
                                className="edit-button"
                                icon={<EditOutlined />}
                                onClick={() => handleStartEdit(index, step)}
                              ></Button>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="scenario-footer">
                    <Button
                      type="primary"
                      size="large"
                      block
                      className="confirm-button"
                      onClick={() => onConfirmScenario?.(localSteps)}
                    >
                      确认无误,开始任务
                    </Button>
                  </div>
                </>
              ) : (
                <div ref={scrollRef} className='scenario-stream'>
                  <StreamTypewriter text={sceneStream} 
                  onchange={() => {
                    scrollRef.current?.scrollTo({
                      top: scrollRef.current.scrollHeight,
                      behavior: "smooth",
                    })
                  }}/>
                </div>
              )}
            </div>
          )}
        </div>
      ) : (
        <div
          style={{
            color: '#999999',
            fontWeight: 'bold',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '100%',
          }}
        >
          评审中...
        </div>
      )}
    </div>
  )
}

export default ReviewResult
