.interview-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      // Tab内容区域样式
      .analysis-content,
      .matching-content,
      .question-content,
      .meeting-content,
      .report-content {
        padding: 10px;
        height: 100%;
        overflow-y: auto;
        
        .component-wrapper {
          height: 100%;
        }
      }
      
      // 分析内容样式
      .analysis-content {
        .component-wrapper {
          // border-left: 4px solid #52c41a;
        }
        
        // 加载状态样式
                    .loading-state {
              display: flex;
              justify-content: center;
              align-items: center;
              min-height: 300px;
              text-align: center;
              
              .loading-text {
                h3 {
                  color: #1890ff;
                  margin-bottom: 16px;
                  font-size: 20px;
                }
                
                p {
                  color: #666;
                  margin-bottom: 24px;
                  font-size: 14px;
                }
              }
            }
        
        // 分析结果样式
        .analysis-result {
          padding: 20px;
          
          h3 {
            color: #52c41a;
            margin-bottom: 16px;
            font-size: 18px;
          }
          
          .result-content {
            background: #f8f9fa;
            border: 1px solid #e8e8e8;
            border-radius: 6px;
            padding: 16px;
            
            pre {
              margin: 0;
              white-space: pre-wrap;
              word-wrap: break-word;
              font-size: 12px;
              color: #333;
            }
          }
        }
      }
      
      // 匹配内容样式
      .matching-content {
        .component-wrapper {
          // border-left: 4px solid #1890ff;
        }
      }

      // 题目内容样式
      .question-content {
        .question-generation {
          h3 {
            color: #1890ff;
            margin-bottom: 16px;
            font-size: 16px;
          }
          
          .question-item {
            margin-bottom: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border-left: 3px solid #1890ff;
            
            h4 {
              color: #262626;
              margin-bottom: 12px;
              font-size: 14px;
              line-height: 1.6;
            }
            
            .question-details {
              p {
                margin-bottom: 8px;
                color: #666;
                font-size: 13px;
                
                &:last-child {
                  margin-bottom: 0;
                }
              }
            }
          }
        }
      }
      
      // 会议内容样式
      // 先注掉
      // .meeting-content {
      //   .interview-meeting {
      //     .meeting-info {
      //       p {
      //         margin-bottom: 8px;
      //         color: #666;
              
      //         &:last-child {
      //           margin-bottom: 0;
      //         }
      //       }
      //     }
          
      //     .meeting-notes {
      //       h4 {
      //         color: #1890ff;
      //         margin-bottom: 12px;
      //       }
            
      //       p {
      //         color: #666;
      //         line-height: 1.6;
      //       }
      //     }
      //   }
      // }

      .meeting-content {
        .interview-meeting {
          max-width: 800px;
          margin: 0 auto;
          padding: 20px;
          
          .meeting-header {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 30px;
            
            h3 {
              margin: 0;
              font-size: 18px;
              color: #333;
            }
            
            .meeting-info-icon {
              width: 16px;
              height: 16px;
              border-radius: 50%;
              background: #1890ff;
              color: white;
              display: flex;
              align-items: center;
              justify-content: center;
              font-size: 12px;
              font-weight: bold;
            }
          }
          
          .meeting-form {
            background: rgba(0, 0, 0, 0.8);
            border-radius: 12px;
            padding: 40px;
            backdrop-filter: blur(10px);
            
            .logo-section {
              text-align: center;
              margin-bottom: 30px;
              
              .meeting-logo {
                width: 80px;
                height: 80px;
                border-radius: 50%;
                background: #1890ff;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 36px;
                font-weight: bold;
                margin: 0 auto 20px;
              }
              
              .meeting-title {
                color: white;
                margin: 0;
                font-size: 20px;
                font-weight: 500;
              }
            }
            
            .form-fields {
              margin-bottom: 20px;
              
              .meeting-input {
                width: 100%;
                padding: 12px 16px;
                border: 1px solid rgba(255, 255, 255, 0.3);
                border-radius: 6px;
                background: rgba(255, 255, 255, 0.1);
                color: white;
                margin-bottom: 16px;
                font-size: 14px;
                
                &::placeholder {
                  color: rgba(255, 255, 255, 0.7);
                }
                
                &:focus {
                  outline: none;
                  border-color: #1890ff;
                  background: rgba(255, 255, 255, 0.15);
                }
              }
              
              .login-button {
                width: 100%;
                padding: 14px;
                background: #1890ff;
                color: white;
                border: none;
                border-radius: 6px;
                font-size: 16px;
                font-weight: 500;
                cursor: pointer;
                transition: background 0.3s;
                
                &:hover {
                  background: #40a9ff;
                }
              }
            }
            
            .meeting-links {
              text-align: center;
              
              .agreement-text {
                color: rgba(255, 255, 255, 0.8);
                font-size: 14px;
                margin-bottom: 8px;
                
                .link {
                  color: #1890ff;
                  text-decoration: none;
                  margin: 0 4px;
                  
                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
              
              .browser-tip {
                color: rgba(255, 255, 255, 0.6);
                font-size: 12px;
                margin: 0;
              }
            }
          }
          
          .host-info {
            // background: #f5f5f5;
            border-radius: 8px;
            padding: 10px;
            
            h4 {
              margin: 0 0 16px 0;
              color: #333;
              font-size: 16px;
              font-weight: 500;
            }
            
            .info-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 8px 0;
              margin-right: 20px;
              // border-bottom: 1px solid #e8e8e8;
              
              &:last-child {
                border-bottom: none;
              }
              
              .label {
                color: #666;
                font-size: 14px;
              }
              
              .value {
                color: #333;
                font-size: 14px;
                font-weight: 500;
                font-family: monospace;
              }
            }
          }
        }
      }
      
      // 报告内容样式
      .report-content {
        .evaluation-report {
          .report-summary {
            p {
              margin-bottom: 8px;
              color: #666;
              font-size: 14px;
              
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
          
          .report-details {
            h4 {
              color: #1890ff;
              margin-bottom: 12px;
            }
            
            p {
              color: #666;
              line-height: 1.6;
            }
          }
        }
      }
    }
  }
  
  .bottom-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 简历预览样式
.resume-preview {
  .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .resume-info {
      flex: 1;
      
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }
      
      .basic-info {
        margin: 8px 0;
        color: #666;
        font-size: 14px;
      }
      
      .contact-info {
        margin: 8px 0;
        color: #999;
        font-size: 13px;
      }
    }
    
    .resume-avatar {
      margin-left: 20px;
    }
  }
  
  .resume-section {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
    
    .advantage-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
      }
    }
    
    .work-item {
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      border-left: 3px solid #1890ff;
      
      .work-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .company {
          font-weight: 600;
          color: #1890ff;
          font-size: 14px;
        }
        
        .position {
          color: #333;
          font-size: 14px;
        }
        
        .duration {
          color: #999;
          font-size: 12px;
        }
      }
      
      .work-description {
        margin: 0;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

// 简历解析样式
.resume-analysis {
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  .candidate-info {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
    }
    
    .candidate-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        
        &.ant-tag-red {
          background: #fff2f0;
          border-color: #ffccc7;
          color: #cf1322;
        }
        
        &.ant-tag-green {
          background: #f6ffed;
          border-color: #b7eb8f;
          color: #389e0d;
        }
      }
    }
    
    .candidate-details {
      margin: 0;
      color: #666;
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  .analysis-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
        border-radius: 2px;
      }
    }
    
    .highlights-list,
    .risks-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        
        &.risk-item {
          color: #cf1322;
        }
      }
    }
  }
}

// 模板内容样式
.template-content {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 岗位匹配样式
.job-matching {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 底部按钮区域样式
.right-buttons {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  
  .button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    
    .ant-btn {
      min-width: 100px;
      height: 36px;
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
      
      &:not(.ant-btn-primary) {
        border-color: #d9d9d9;
        color: #666;
        
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }
}

// // 响应式设计
// @media (max-width: 1200px) {
//   .interview-module {
//     .interview-container {
//       flex-direction: column;
//       height: auto;
//       
//       .left-section,
//       .right-section {
//         flex: none;
//         height: auto;
//         min-height: 400px;
//       }
//     }
//   }
// }

// @media (max-width: 768px) {
//   .interview-module {
//     padding: 10px;
//     
//     .interview-container {
//       gap: 10px;
//       
//       .left-section,
//       .right-section {
//         .left-tabs,
//         .right-tabs {
//           .ant-tabs-tabpane {
//             padding: 15px;
//           }
//         }
//       }
//     }
//     
//     .bottom-actions {
//       flex-direction: column;
//       align-items: center;
//       
//       .ant-btn {
//         width: 100%;
//         max-width: 200px;
//       }
//     }
//   }
// }

// 加载状态样式已使用Ant Design的Spin组件
