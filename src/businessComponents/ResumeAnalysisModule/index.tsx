import React, {
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from 'react'
import { Tabs} from 'antd'
import type { TabsProps } from 'antd'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'
import TabContainer from '../TabContainer'
import './index.less'
import AnalysisModule from './component/AnalysisModule' // 简历解析
import JobMatchingModule from './component/JobMatchingModule' // 岗位匹配
import ReviewResultModal from '@/component/ReviewResultModal'
// import MarkdownRenderer from '@/businessComponents/MarkdownModule'
import StreamTypewriter from '@/component/StreamTypewriter'

export interface ResumeAnalysisModuleRef {
  getMentionsData: () => any
}

interface ResumeAnalysisModuleProps {
  leftTabs?: any[]
  stepIndex?: number
  fileData?: any // 文件数据
  startData?: {
    // 开始流程时的数据
    query: string
    localFile: any[]
    timestamp: number
  }
  agentId?: string // agentId
  setGlobalLoading?: (loading: boolean) => void
  globalLoading?: boolean // 新增：全局加载状态
  pageInfo?: any
  currentEchoData?: any
  onCallParent?: (type: string, data?: any) => void
}

const ResumeAnalysisModule = forwardRef<ResumeAnalysisModuleRef, ResumeAnalysisModuleProps>(
  (
    {
      leftTabs = [],
      stepIndex = 0,
      fileData,
      startData, // 开始流程时的数据
      agentId, // agentId
      setGlobalLoading,
      globalLoading,
      pageInfo,
      currentEchoData,
      onCallParent
    },
    ref
  ) => {
    // debugger
    console.log(leftTabs, '--------leftTabs')
    const sseChat = useSSEChat()
    const [rightActiveTab, setRightActiveTab] = useState('analysis')
    const tabContainerRef = useRef<any>(null)
    const questionModuleRef = useRef<any>(null) // 题目模块的ref

    // 新增：简历解析结果状态
    const [resumeAnalysisResult, setResumeAnalysisResult] = useState<any>(null)
    // 新增：简历分析数据的细分状态
    const [basicAnalysisResult, setBasicAnalysisResult] = useState<any>(null) // 存储除了job_match之外的简历分析数据
    const [jobMatchResult, setJobMatchResult] = useState<any>(null) // 存储job_match键值对

    // 测试数据：模拟API返回的JSON数据
    const testJsonData = `\`\`\`json
{
  "basic_info": {
    "name": "王莹",
    "age": 34,
    "gender": "女",
    "phone": "15330231578",
    "email": "<EMAIL>",
    "total_experience_years": 11,
    "highest_education": {
      "degree_level": "本科",
      "school": "天津外国语大学",
      "major": "财务管理",
      "graduation_date": "2014-06"
    }
  },
  "highlights": [
    "具备8年以上产品管理经验，3年以上AI产品经理工作经验，符合10年经验的岗位要求。",
    "主导多个AI相关项目，如AI数字人、大语言模型智能助理等，具备从0到1的产品落地经验，与AI产品经理岗位高度契合。"
  ],
  "risk_sentences": [
    "时间匹配不一致：简历显示"参加工作时间"为2015年，但第一段工作经历为2015.03，相差不足3个月，基本匹配。",
    "未发现相关风险",
    "未发现相关风险"
  ],
  "meta": {
    "notes": "估算毕业时间为2014-06，因简历中未提供具体月份。"
  },
  "job_match": {
    "score_percent": 88,
    "rationale": [
      "核心技能匹配度高，具备NPDP认证及AI产品设计经验，符合JD要求.",
      "工作经验11年，与JD要求的10年经验匹配度良好，且有多个AI项目独立负责经历.",
      "职责覆盖AI产品全流程，与JD中要求的产品设计、AI赋能等场景高度重合.",
      "具备AI、金融科技、电商等多元行业经验，与JD行业相关性较高.",
      "具备NPDP认证，学历满足门槛要求.",
      "具备跨部门协作与项目管理能力，符合通用能力要求."
    ],
    "dimension_scores_charts": {
      "backgroundColor": "#fff",
      "tooltip": {},
      "radar": {
        "center": ["50%", "50%"],
        "radius": "70%",
        "name": {
          "textStyle": {
            "fontSize": 14,
            "fontFamily": "sider-font, Arial, sans-serif"
          }
        },
        "indicator": [
          { "name": "核心技能匹配", "max": 30 },
          { "name": "经验年限与层级", "max": 20 },
          { "name": "职责与场景匹配", "max": 20 },
          { "name": "域知识与行业", "max": 10 },
          { "name": "学历与证书", "max": 10 },
          { "name": "软技能与通用能力", "max": 10 }
        ],
        "fontFamily": "sider-font"
      },
      "series": [
        {
          "name": "岗位匹配度",
          "type": "radar",
          "data": [
            {
              "value": [28, 19, 18, 9, 9, 9],
              "name": "岗位匹配度",
              "areaStyle": { "opacity": 0.2 },
              "lineStyle": { "width": 2 },
              "itemStyle": { "color": "blue" }
            }
          ]
        }
      ]
    }
  }
}
\`\`\`

\`\`\`json
{
    "评审结果": "通过",
    "评审细节": [
        {
            "参数名": "基本信息",
            "校验结果": "通过",
            "说明": "基本信息完整且无矛盾",
            "异常处理建议": "无需处理"
        },
        {
            "参数名": "工作经验年限",
            "校验结果": "通过",
            "说明": "11年工作经验符合岗位要求",
            "异常处理建议": "无需处理"
        },
        {
            "参数名": "岗位匹配度",
            "校验结果": "通过",
            "说明": "核心技能与岗位高度匹配",
            "异常处理建议": "无需处理"
        },
        {
            "参数名": "风险提示",
            "校验结果": "通过",
            "说明": "未发现重大风险项",
            "异常处理建议": "无需处理"
        },
        {
            "参数名": "学历与证书",
            "校验结果": "通过",
            "说明": "学历及NPDP认证符合要求",
            "异常处理建议": "无需处理"
        }
    ]
}`

    // 全局加载状态管理
    // const [globalLoading, setGlobalLoading] = useState(false);
    const [loadingText, setLoadingText] = useState('正在处理...')
    const [reviewVisible, setReviewVisible] = useState<boolean>(false) // 输入评审弹框
    const [reviewData, setReviewData] = useState<any>([]) // 输入评审弹框数据
    const [isType, setIsType] = useState<string>('否') // 是否忽略错误
    const [outputReviewData, setOutputReviewData] = useState<any>([]) // 输出评审数据

    // 自动开始逻辑：当组件挂载且有开始数据时，根据当前步骤自动调用相应的接口
    useEffect(() => {
      if(currentEchoData && Object.keys(currentEchoData).length > 0) {
        console.log(currentEchoData, 'currentEchoData')
        if(currentEchoData.resumeAnalysisResult) {
          setResumeAnalysisResult(currentEchoData.resumeAnalysisResult)
          setBasicAnalysisResult(currentEchoData.basicAnalysisResult)
          setJobMatchResult(currentEchoData.jobMatchResult)
        }
      } 
      // else {
      //   console.log('没有回显内容')
      //   handleAutoStartResumeAnalysis()
  
      // }
    }, [])

    // 自动开始简历解析
    const handleAutoStartResumeAnalysis = async () => {
      // debugger
      console.log('handleAutoStartResumeAnalysis调用了！！！！')
      console.log('startData:', startData)
      console.log('agentId:', agentId)
      if (startData && agentId) {
        setGlobalLoading?.(true)
        await callUnifiedAPI(startData, agentId)
      }
    }

    // 统一的API接口调用方法
    const callUnifiedAPI = async (
      startData: any,
      agentId: string,
    ) => {
      try {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')

        // 准备文件数据
        const fileData: any[] = []
        startData.localFile.forEach((item: any) => {
          fileData.push({
            type: item.fileType || 'document',
            transfer_method: 'local_file',
            upload_file_id: item.id,
          })
        })

        let resumeAnalysisResult: any = null
        // 调用流式接口
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },
          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            query: startData.query || '',
            difyJson: {
              inputs: {
                files: fileData,
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                outputTemplate: null,
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: startData.query || '',
              files: fileData,
            },
          },
          query: {},
          message: startData.query || '',
          onMessage: (message) => {
            // console.log(`接口返回消息:`, message)
            // 去掉 <think> 标签内容
            let cleanStr = message
              .replace(/<think>[\s\S]*?<\/think>/g, '')
              .trim()
              setResumeAnalysisResult(cleanStr)
              resumeAnalysisResult = cleanStr

            try {
            //   // 提取 JSON
              // const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/)
            //   console.log('提取的JSON:', jsonMatch)
              // if (jsonMatch) {
              //   const parsedData = JSON.parse(jsonMatch[1].trim())
              //   setResumeAnalysisResult(parsedData)
              //   resumeAnalysisResult = parsedData
            //     return

            //     // 判断第一个JSON里有没有"评审结果"这一项
            //     if (parsedData['评审结果' as keyof typeof parsedData]) {
            //       // 如果有"评审结果"，说明是评审数据
            //       console.log('检测到评审结果:', parsedData)
            //       if (
            //         parsedData['评审结果' as keyof typeof parsedData] === '异常'
            //       ) {
            //         setReviewData([parsedData])
            //         setReviewVisible(true)
            //       }
            //     } else {
            //       // 如果没有"评审结果"，说明是简历解析数据
            //       console.log('检测到简历解析结果:', parsedData)
            //       setResumeAnalysisResult(parsedData)
            //       resumeAnalysisResult = parsedData

            //       //  // 2. 提取job_match键值对到jobMatchResult变量
            //       // if (parsedData.job_match) {
            //       //   setJobMatchResult(parsedData.job_match);
            //       //   console.log('提取的job_match数据:', parsedData.job_match);
            //       // }

            //       // // 3. 将除了job_match之外的其余内容存储到basicAnalysisResult变量
            //       // const { job_match, ...restData } = parsedData;
            //       // setBasicAnalysisResult(restData);
            //       // console.log('提取的基础分析数据:', restData);
            //     }
              // }
            } catch (e) {
              console.error('JSON 解析失败', e, cleanStr)
            }
            // console.log('cleanStr:', cleanStr)
          },
          onFinished: (res) => {
            console.log('接口完成:', res)
            setGlobalLoading?.(false)
            let cleanStr = res
              .replace(/<think>[\s\S]*?<\/think>/g, '')
              .trim()
            // 去掉```json```标识
            const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/)
            // console.log('提取的JSON:', jsonMatch)
            if (jsonMatch) {
            // 2. 提取job_match键值对到jobMatchResult变量
              const parsedData = JSON.parse(jsonMatch[1].trim())
              if (parsedData.job_match) {
                setJobMatchResult(parsedData.job_match)
                console.log('提取的job_match数据:',parsedData.job_match
                )
              }
  
              // 3. 将除了job_match之外的其余内容存储到basicAnalysisResult变量
              const { job_match, ...restData } = parsedData
              setBasicAnalysisResult(restData)
              console.log('提取的基础分析数据:', restData)

            }
            // 先注掉单元输入输出评审
            if (!currentEchoData?.outReview) {
              onCallParent?.("输出", cleanStr);
            }
            
            
          },
        })
      } catch (error) {
        console.error(`接口调用失败:`, error)
        setGlobalLoading?.(false)
      }
    }

    // 暴露方法给父组件
    useImperativeHandle(
      ref,
      () => ({
        getMentionsData: () => {
          return {
            outputReviewData: outputReviewData,
            info: `${startData?.query},${JSON.stringify(resumeAnalysisResult)}`,
            resumeAnalysisResult: resumeAnalysisResult,
            jobMatchResult: jobMatchResult,
            basicAnalysisResult: basicAnalysisResult,
          }
        },
        handleAutoStartResumeAnalysis: handleAutoStartResumeAnalysis,
      }),
    )

    const items: TabsProps['items'] = [
      {
        key: '1',
        label: '简历预览',
        children: (<embed
          style={{ width: '100%', height: '100%', minHeight: 'calc(100vh - 165px)' }}
          type='application/pdf'
          src={fileData[0].url + '#toolbar=0&navpanes=0&scrollbar=0'}
        />),
      }
    ];
    const openModal = () => {
      setReviewVisible(true)
      setReviewData([outputReviewData])
    }

    return (
      <div className="interview-module">
        <div className="interview-container">
          {/* 左侧动态Tab内容 */}
          <div className="left-section">
              {/* <Tabs 
                items={items}
                className="dynamic-tabs"
                type="card"
                size="small"
              /> */}
          
                <TabContainer
                ref={tabContainerRef}
                tabs={leftTabs}
                stepIndex={0}
                className="left-tabs"
              />
          </div>

          {/* 右侧内容区域 */}
          <div className="right-section">
            {/* 根据步骤动态显示不同的右侧内容 */}
                <div className="analysis-content">
                  <div className="component-wrapper">
                    {/* 加载中且有简历解析结果时显示打字机效果 */}
                    {globalLoading && resumeAnalysisResult && (
                      <StreamTypewriter
                        text={resumeAnalysisResult}
                      />
                    )}
                    {/* {resumeAnalysisResult && (
                      <StreamTypewriter
                        text={resumeAnalysisResult}
                      />
                    )} */}
                    {basicAnalysisResult && jobMatchResult && (
                      <>
                        {/* 简历解析步骤：显示简历解析和岗位匹配度两个tab */}
                        <Tabs
                          activeKey={rightActiveTab}
                          onChange={setRightActiveTab}
                          items={[
                            {
                              key: 'analysis',
                              label: '简历解析',
                              children: (
                                <div className="analysis-content">
                                  <div className="component-wrapper">
                                    <AnalysisModule
                                      viewData={basicAnalysisResult}
                                    />
                                  </div>
                                </div>
                              ),
                            },
                            {
                              key: 'matching',
                              label: '岗位匹配度',
                              children: (
                                <div className="matching-content">
                                  <div className="component-wrapper">
                                    <JobMatchingModule
                                      viewData={jobMatchResult}
                                    />
                                  </div>
                                </div>
                              ),
                            },
                          ]}
                          className="right-tabs"
                        />
                      </>
                    )}
                  </div>
                </div>
                {/* {basicAnalysisResult && jobMatchResult && (
                  <div style={{ textAlign: 'right', color: '#1777FF',cursor: 'pointer', padding: '0 20px' }} onClick={openModal}>输出校验结果：{outputReviewData['评审结果']}</div>
                )} */}
                

            {/* 右侧按钮区域 */}
            {/* <div className="right-buttons">
              <div className="button-group">
                {buttons.showPrevious && (
                  <Button onClick={handlePrevious} style={{ marginRight: 8 }}>
                    上一步
                  </Button>
                )}

                {buttons.showNext && (
                  <Button
                    type="primary"
                    onClick={handleNext}
                    style={{ marginRight: 8 }}
                  >
                    下一步
                  </Button>
                )}

                {buttons.showRestart && (
                  <Button onClick={handleRestart} style={{ marginRight: 8 }}>
                    重新开始
                  </Button>
                )}

                {buttons.showComplete && (
                  <Button
                    type="primary"
                    onClick={handleComplete}
                    style={{ marginRight: 8 }}
                  >
                    完成面试
                  </Button>
                )}

                {buttons.showDownload && (
                  <Button type="primary" onClick={handleDownload}>
                    下载报告
                  </Button>
                )}
              </div>
            </div> */}
          </div>
        </div>
        {/* <ReviewResultModal
          visible={reviewVisible}
          onCancel={() => setReviewVisible(false)}
          onContinue={() => {
            setIsType('是')
            setReviewVisible(false) // 关闭弹窗
            handleAutoStartResumeAnalysis()
            // runSequentially();
          }}
          reviewItems={reviewData}
          finalConclusion="不通过"
        /> */}
      </div>
    )
  }
)

ResumeAnalysisModule.displayName = 'ResumeAnalysisModule'

export default ResumeAnalysisModule
