.video-interview-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 50px 20px 20px 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      
      .left-tabs {
        height: 100%;
        
        .ant-tabs-content-holder {
          height: calc(100% - 46px);
          overflow-y: auto;
        }
        
        .ant-tabs-tabpane {
          height: 100%;
          // padding: 20px;
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: white;
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      display: flex;
      flex-direction: column;
      
      .right-tabs {
        flex: 1; // Changed from height: 100%
      }

      .meeting-content {
        padding: 0 10px;
        height: 100%;
        overflow-y: auto;
        
        .component-wrapper {
          height: 100%;
          border-radius: 8px;
          .meeting-form{
            height: calc(100vh - 315px) !important;
            margin-top: 20px;
          }
          .host-info {
            margin-top: 20px;
          }
          
        }
        .iframe-container {
          height: 100%;
          border-radius: 8px;
          
          iframe {
            height: 100% !important;
          }
        }
      }

    }
  }
  
  .bottom-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

// 简历预览样式
.resume-preview {
  .resume-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
    
    .resume-info {
      flex: 1;
      
      h2 {
        margin: 0 0 8px 0;
        color: #1890ff;
        font-size: 24px;
        font-weight: 600;
      }
      
      .basic-info {
        margin: 8px 0;
        color: #666;
        font-size: 14px;
      }
      
      .contact-info {
        margin: 8px 0;
        color: #999;
        font-size: 13px;
      }
    }
    
    .resume-avatar {
      margin-left: 20px;
    }
  }
  
  .resume-section {
    margin-bottom: 24px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 16px;
      font-weight: 600;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
    
    .advantage-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
      }
    }
    
    .work-item {
      margin-bottom: 16px;
      padding: 16px;
      background: #fafafa;
      border-radius: 6px;
      border-left: 3px solid #1890ff;
      
      .work-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .company {
          font-weight: 600;
          color: #1890ff;
          font-size: 14px;
        }
        
        .position {
          color: #333;
          font-size: 14px;
        }
        
        .duration {
          color: #999;
          font-size: 12px;
        }
      }
      
      .work-description {
        margin: 0;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
      }
    }
  }
}

// 简历解析样式
.resume-analysis {
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05),0px 3px 6px -4px rgba(0, 0, 0, 0.12),0px 6px 16px 0px rgba(0, 0, 0, 0.08);
  .candidate-info {
    margin-bottom: 20px;
    
    h3 {
      margin: 0 0 12px 0;
      color: #1890ff;
      font-size: 20px;
      font-weight: 600;
    }
    
    .candidate-tags {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      margin-bottom: 12px;
      
      .ant-tag {
        margin: 0;
        padding: 4px 12px;
        border-radius: 16px;
        font-size: 12px;
        
        &.ant-tag-red {
          background: #fff2f0;
          border-color: #ffccc7;
          color: #cf1322;
        }
        
        &.ant-tag-green {
          background: #f6ffed;
          border-color: #b7eb8f;
          color: #389e0d;
        }
      }
    }
    
    .candidate-details {
      margin: 0;
      color: #666;
      font-size: 13px;
      line-height: 1.5;
    }
  }
  
  .analysis-section {
    margin-bottom: 20px;
    
    h4 {
      margin: 0 0 12px 0;
      color: #333;
      font-size: 14px;
      font-weight: 600;
      display: flex;
      align-items: center;
      
      &::before {
        content: '';
        width: 4px;
        height: 16px;
        background: #1890ff;
        margin-right: 8px;
        border-radius: 2px;
      }
    }
    
    .highlights-list,
    .risks-list {
      margin: 0;
      padding-left: 20px;
      
      li {
        margin-bottom: 8px;
        color: #666;
        font-size: 13px;
        line-height: 1.5;
        
        &.risk-item {
          color: #cf1322;
        }
      }
    }
  }
}

// 模板内容样式
.template-content {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 岗位匹配样式
.job-matching {
  padding: 40px;
  text-align: center;
  color: #999;
  font-size: 16px;
}

// 底部按钮区域样式
.right-buttons {
  margin-top: 24px;
  padding: 16px 0;
  border-top: 1px solid #e8e8e8;
  
  .button-group {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 8px;
    
    .ant-btn {
      min-width: 100px;
      height: 36px;
      border-radius: 6px;
      font-weight: 500;
      
      &.ant-btn-primary {
        background: #1890ff;
        border-color: #1890ff;
        
        &:hover {
          background: #40a9ff;
          border-color: #40a9ff;
        }
      }
      
      &:not(.ant-btn-primary) {
        border-color: #d9d9d9;
        color: #666;
        
        &:hover {
          border-color: #1890ff;
          color: #1890ff;
        }
      }
    }
  }
}

// // 响应式设计
// @media (max-width: 1200px) {
//   .interview-module {
//     .interview-container {
//       flex-direction: column;
//       height: auto;
//       
//       .left-section,
//       .right-section {
//         flex: none;
//         height: auto;
//         min-height: 400px;
//       }
//     }
//   }
// }

// @media (max-width: 768px) {
//   .interview-module {
//     padding: 10px;
//     
//     .interview-container {
//       gap: 10px;
//       
//       .left-section,
//       .right-section {
//         .left-tabs,
//         .right-tabs {
//           .ant-tabs-tabpane {
//             padding: 15px;
//           }
//         }
//       }
//     }
//     
//     .bottom-actions {
//       flex-direction: column;
//       align-items: center;
//       
//       .ant-btn {
//         width: 100%;
//         max-width: 200px;
//       }
//     }
//   }
// }

// 加载状态样式已使用Ant Design的Spin组件
