import React, {
  useState,
  useRef,
  useImperativeHandle,
  forwardRef,
  useEffect,
} from 'react'
import { Tabs, Card, Tag, Button, Avatar, Divider, Spin } from 'antd'
import type { TabsProps } from 'antd'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'
import TabContainer, { TabItem } from '../TabContainer'
import './index.less'
import AnalysisModule from '../AnalysisModule' // 修复导入路径
import JobMatchingModule from '../JobMatchingModule' // 新增导入路径
import QuestionModule from './component/QuestionModule' // 新增导入路径
import ReviewResultModal from '@/component/ReviewResultModal'
import MarkdownRenderer from '@/businessComponents/MarkdownModule'
import StreamTypewriter from '@/component/StreamTypewriter'

// const { TabPane } = Tabs

export interface IntelligentQuestionModuleRef {
  getMentionsData: () => any
}

interface IntelligentQuestionModuleProps {
  leftTabs?: TabItem[]
  currentEchoData?: any
  fileData?: any // 文件数据
  agentId?: string // agentId
  setGlobalLoading?: (loading: boolean) => void
  globalLoading?: boolean // 新增：全局加载状态
  questionData?: any
  pageInfo?: any
  stepIndex?: number // 新增：步骤索引
  onCallParent?: (type: string, data?: any) => void
}

const IntelligentQuestionModule = forwardRef<
  IntelligentQuestionModuleRef,
  IntelligentQuestionModuleProps
>(
  (
    {
      leftTabs = [],
      currentEchoData,
      fileData,
      agentId,
      setGlobalLoading,
      globalLoading,
      questionData,
      pageInfo,
      stepIndex,
      onCallParent,
    },
    ref
  ) => {
    const sseChat = useSSEChat()
    const tabContainerRef = useRef<any>(null)
    const questionModuleRef = useRef<any>(null) // 题目模块的ref

    // 新增：简历解析结果状态
    const [questionResult, setQuestionResult] = useState<any>(null)
    // {
    //   技术能力: {
    //     Q1: {
    //       问题: '请描述你在信息类产品中，如何设计一个高可用、高扩展性的技术架构？请结合你过往的项目经验说明。',
    //       参考答案:
    //         '在我们之前开发的一个大型信息聚合平台中，我主导了后端架构的设计。为了实现高可用和高扩展性，我们采用了微服务架构，将核心功能模块（如数据抓取、内容推荐、用户行为分析）拆分成独立的服务。每个服务独立部署，使用Kubernetes进行容器编排，确保弹性伸缩。同时，我们引入了Redis作为缓存层，减轻数据库压力，并使用Kafka进行异步消息处理，提升系统的吞吐能力。在数据层面，我们采用了Elasticsearch来支持高效的全文检索，保证用户搜索体验。',
    //       考察点: '架构设计能力、对高可用与扩展性的理解、技术选型的合理性',
    //       追问建议:
    //         '你是如何评估不同技术方案的优劣？在实际部署过程中遇到过哪些技术瓶颈？如何解决的？',
    //     },
    //   },
    //   项目经验: {
    //     Q1: {
    //       问题: '请分享一个你主导或深度参与的、从0到1的信息类产品项目。请描述项目的背景、你的角色、所用技术及最终成果。',
    //       参考答案:
    //         '我曾主导一个企业级知识管理平台的开发。该项目的背景是某大型金融机构内部知识沉淀不足，员工信息查找效率低。我担任产品负责人，负责需求调研、产品设计和项目管理。我们采用React + Node.js + MongoDB的栈进行开发，引入AI语义搜索技术提升搜索精度。项目上线后，知识检索效率提升了60%，员工满意度显著提高，并获得了公司年度创新奖。',
    //       考察点: '项目管理能力、产品设计与落地经验、成果导向思维',
    //       追问建议:
    //         '在项目过程中遇到过哪些关键挑战？你是如何协调资源和推动团队解决的？',
    //     },
    //   },
    //   团队协作: {
    //     Q1: {
    //       问题: '请描述一次你与技术团队、业务方或跨部门团队之间存在分歧的协作经历。你是如何推动达成一致的？',
    //       参考答案:
    //         '在一次信息平台的重构项目中，技术团队希望采用全新的技术栈以提升系统性能，而业务方担心迁移成本和上线风险。我组织了多次跨部门会议，邀请技术团队进行架构说明，并与业务方共同制定迁移路线图，分阶段上线。最终通过引入灰度发布和A/B测试策略，逐步验证新系统稳定性，最终获得各方认可。',
    //       考察点: '跨部门沟通能力、冲突解决能力、项目协调能力',
    //       追问建议: '你是如何评估不同团队的意见，并做出最终决策的？',
    //     },
    //   },
    //   问题解决: {
    //     Q1: {
    //       问题: '请分享一个你在项目中遇到的复杂问题，你是如何分析问题并找到解决方案的？',
    //       参考答案:
    //         '在信息平台上线初期，我们发现部分用户在高并发访问时出现页面加载缓慢甚至崩溃的情况。我首先通过日志分析和性能监控工具定位问题，发现是数据库查询效率低。随后，我与DBA团队合作优化SQL语句，引入缓存机制，并对热点数据进行预加载，最终将页面加载时间从5秒缩短至1秒以内。',
    //       考察点: '问题分析能力、技术深度、系统优化能力',
    //       追问建议:
    //         '你是如何判断问题的根本原因的？是否考虑过其他可能的解决方案？',
    //     },
    //   },
    //   学习能力: {
    //     Q1: {
    //       问题: '请举例说明你最近学习的一项新技术或新工具，并说明你是如何将它应用到实际工作中的。',
    //       参考答案:
    //         '我最近学习了LangChain和大模型提示工程，用于优化我们信息平台的内容推荐算法。通过学习相关文档和社区资源，我快速掌握了如何构建基于LLM的推荐逻辑，并在项目中进行了小范围试点，最终提升了推荐内容的相关性和用户点击率。',
    //       考察点: '持续学习能力、新技术落地能力、实际应用效果',
    //       追问建议:
    //         '你是如何评估学习资源的有效性？在学习过程中遇到过哪些困难？如何克服的？',
    //     },
    //   },
    //   沟通表达: {
    //     Q1: {
    //       问题: '请描述一次你需要向非技术背景的高层领导汇报产品进展的经历。你如何组织语言和呈现内容以确保对方理解？',
    //       参考答案:
    //         '在一次产品复盘汇报中，我向公司CTO汇报了一个信息平台的升级计划。我采用了‘目标-现状-方案-价值’的结构，用图表展示用户增长和性能提升数据，避免使用过多技术术语，而是用业务语言解释技术决策的价值。例如，我将‘微服务架构’简化为‘模块化升级，便于快速迭代’，让CTO能够快速理解并支持项目。',
    //       考察点: '业务沟通能力、信息简化与结构化表达能力',
    //       追问建议:
    //         '你是如何判断听众的理解水平和关注点的？在汇报过程中是否遇到过质疑？如何应对？',
    //     },
    //   },
    // }

    // 全局加载状态管理
    // const [globalLoading, setGlobalLoading] = useState(false);
    // const [loadingText, setLoadingText] = useState('正在处理...')
    const [reviewVisible, setReviewVisible] = useState<boolean>(false) // 输入评审弹框
    const [reviewData, setReviewData] = useState<any>([]) // 输入评审弹框数据
    const [isType, setIsType] = useState<string>('否') // 是否忽略错误
    const [outputReviewData, setOutputReviewData] = useState<any>([]) // 输出评审数据

    // 自动开始逻辑：当组件挂载且有开始数据时，根据当前步骤自动调用相应的接口
    useEffect(() => {
      console.log('智能出题传参:', questionData)
      console.log('智能出题回显数据:', currentEchoData)
      console.log('智能出题pageInfo:', pageInfo)
      if (currentEchoData && Object.keys(currentEchoData).length > 0) {
        if(currentEchoData.questionResult) {
          setQuestionResult(currentEchoData.questionResult)
        }
      }
      // else {
      //   console.log('没有回显内容')
      //   handleAutoStart()
      // }
    }, [])

    // 自动开始智能出题
    const handleAutoStart = async () => {
      if (agentId) {
        setGlobalLoading?.(true)
        await callUnifiedAPI(agentId)
      }
    }

    // 统一的API接口调用方法
    const callUnifiedAPI = async (agentId: string) => {
      try {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')
        setQuestionResult(null)

        let questionResult: any = null
        // 调用流式接口
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },
          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            // query: startData.query || '',
            query: '1',
            difyJson: {
              inputs: {
                files: fileData,
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: questionData.queryData.info,
              // files: fileData,
            },
          },
          query: {},
          message: '1',
          onMessage: (message) => {
            // console.log(`接口返回消息:`, message)
            // 去掉 <think> 标签内容
            let cleanStr = message
              .replace(/<think>[\s\S]*?<\/think>/g, '')
              .trim()
              setQuestionResult(cleanStr)
              // questionResult = cleanStr

            try {
              // // 提取 JSON
              // const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/)
              // console.log('提取的JSON:', jsonMatch)
              // if (jsonMatch) {
              //   const parsedData = JSON.parse(jsonMatch[1].trim())
              //   setQuestionResult(parsedData)
              //   questionResult = parsedData

                // // 判断第一个JSON里有没有"评审结果"这一项
                // if (parsedData['评审结果' as keyof typeof parsedData]) {
                //   // 如果有"评审结果"，说明是评审数据
                //   console.log('检测到评审结果:', parsedData)
                //   if (
                //     parsedData['评审结果' as keyof typeof parsedData] === '异常'
                //   ) {
                //     // setReviewData([parsedData])
                //     // setReviewVisible(true)
                //   }
                // } else {
                //   // 如果没有"评审结果"，说明是智能出题数据
                //   console.log('检测到简历解析结果:', parsedData)
                //   setQuestionResult(parsedData)
                //   questionResult = parsedData
                // }
              // }
            } catch (e) {
              console.error('JSON 解析失败', e, cleanStr)
            }
            // console.log('cleanStr:', cleanStr)
          },
          onFinished: (res) => {
            // console.log(`接口完成:`, res)
            setGlobalLoading?.(false)
            let cleanStr = res
            .replace(/<think>[\s\S]*?<\/think>/g, '')
            .trim()
            // 去掉```json```标识
            const jsonMatch = cleanStr.match(/```(?:json)?\s*([\s\S]*?)```/)
            // console.log('提取的JSON:', jsonMatch)
            if (jsonMatch) {
            // 2. 提取job_match键值对到jobMatchResult变量
              const parsedData = JSON.parse(jsonMatch[1].trim())
              setQuestionResult(parsedData)
              // questionResult = parsedData
            }
            // 先注掉单元输入输出评审
            onCallParent?.('输出', cleanStr)
          },
        })
      } catch (error) {
        console.error(`接口调用失败:`, error)
        setGlobalLoading?.(false)
      }
    }
    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      getMentionsData: () => {
        return {
          // outputReviewData: outputReviewData,
          // info: resumeAnalysisResult +  startData?.query,
          // jobMatchResult: jobMatchResult,
          // basicAnalysisResult: basicAnalysisResult,
          questionResult: questionResult,
        }
      },
      handleAutoStart: handleAutoStart,
    }))

    const items: TabsProps['items'] = [
      {
        key: '1',
        label: '简历预览',
        children: (
          <embed
            style={{
              width: '100%',
              height: '100%',
              minHeight: 'calc(100vh - 265px)',
            }}
            type="application/pdf"
            src={fileData[0].url + '#toolbar=0&navpanes=0&scrollbar=0'}
          />
        ),
      },
    ]
    const openModal = () => {
      setReviewVisible(true)
      setReviewData([outputReviewData])
    }

    return (
      <div className="intelligent-question-module">
        <div className="interview-container">
          {/* 左侧动态Tab内容 */}
          <div className="left-section">
            {/* {leftTabs && leftTabs.length > 0 ? (
              <TabContainer
                ref={tabContainerRef}
                tabs={leftTabs}
                currentStep={currentStep}
                className="left-tabs"
              />
            ) : (
              <div className="default-left-content">
                <h3>左侧内容区域</h3>
                <p>请配置leftTabs来显示具体内容</p>
              </div>
            )} */}

            {/* <Tabs
              items={items}
              className="dynamic-tabs"
              type="card"
              size="small"
            /> */}

            <TabContainer
              tabs={leftTabs}
              stepIndex={stepIndex || 0}
              className="left-tabs"
            />
          </div>

          {/* 右侧内容区域 */}
          <div className="right-section">
            <div className="question-content">
              <div className="component-wrapper">
                {globalLoading && questionResult ? (
                  // <Spin tip={loadingText} size="large">
                  //   <div style={{ minHeight: '300px', padding: '20px' }}>
                  //     <div className="loading-text">
                  //       <h3>{loadingText}</h3>
                  //       <p>请稍候，系统正在处理您的请求</p>
                  //     </div>
                  //   </div>
                  // </Spin>
                    <StreamTypewriter
                      text={typeof questionResult === 'string' ? questionResult : JSON.stringify(questionResult)}
                    />
                ) : (
                  <QuestionModule
                    ref={questionModuleRef}
                    viewData={questionResult}
                  />
                )}
              </div>
            </div>
          </div>
        </div>
        <ReviewResultModal
          visible={reviewVisible}
          onCancel={() => setReviewVisible(false)}
          onContinue={() => {
            setIsType('是')
            setReviewVisible(false) // 关闭弹窗
            // runSequentially();
          }}
          reviewItems={reviewData}
          finalConclusion="不通过"
        />
      </div>
    )
  }
)

IntelligentQuestionModule.displayName = 'IntelligentQuestionModule'

export default IntelligentQuestionModule
