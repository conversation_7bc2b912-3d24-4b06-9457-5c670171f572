.transcript-module {
  width: 100%;
  height: 100%;
  // background: #f5f5f5;
  padding: 20px;
  
  .interview-container {
    gap: 30px;
    display: flex;
    height: 100%;
    // min-height: 600px;

    
    
    .left-section {
      // flex: 1;
      width: 40%;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      padding: 12px 12px 16px;
      display: flex;
      flex-direction: column;

      .section-title {
        font-size: 14px;
        color: #333;
        margin: 4px 0 12px 0;
        font-weight: 500;
      }

      .video-preview-card {
        position: relative;
        flex: 1;
        border-radius: 8px;
        overflow: hidden;
        background: #0b1b2b;

        .video-bg {
          position: absolute;
          inset: 0;
          background: url('/public/header-footer-gradient-bg.png') center/cover no-repeat;
          filter: brightness(0.6);
        }

        .login-panel {
          position: relative;
          width: 340px;
          margin: 60px auto 0;
          background: rgba(0, 0, 0, 0.75);
          border-radius: 8px;
          padding: 24px 22px 18px;
          color: #fff;

          .meeting-logo {
            width: 68px;
            height: 68px;
            border-radius: 50%;
            background: #1890ff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 36px;
            font-weight: 700;
            margin: 0 auto 12px;
          }

          .meeting-subtitle {
            text-align: center;
            font-size: 12px;
            color: rgba(255,255,255,0.85);
            margin-bottom: 14px;
          }

          .meeting-input {
            width: 100%;
            padding: 10px 12px;
            margin-bottom: 10px;
            border-radius: 4px;
            border: 1px solid rgba(255,255,255,0.25);
            background: rgba(255,255,255,0.05);
            color: #fff;

            &::placeholder {
              color: rgba(255,255,255,0.7);
            }
          }

          .login-button {
            width: 100%;
            padding: 10px 12px;
            background: #1a73e8;
            border: none;
            color: #fff;
            border-radius: 4px;
            cursor: pointer;
            margin: 4px 0 10px;
          }

          .agreement-text {
            text-align: center;
            font-size: 12px;
            color: rgba(255,255,255,0.8);

            .link {
              color: #1a73e8;
              margin: 0 4px;
            }
          }

          .browser-tip {
            text-align: center;
            font-size: 12px;
            color: rgba(255,255,255,0.6);
            margin: 4px 0 0;
          }
        }
      }
    }
    
    .right-section {
      flex: 1;
      background: #fff;
      border-radius: 8px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      padding: 12px 12px 16px;

      .section-title {
        font-size: 14px;
        color: #333;
        margin: 4px 0 12px 0;
        font-weight: 500;
      }

      .message-list {
        flex: 1;
        overflow-y: auto;
        padding-right: 4px;
        background-color: #f2f4fa;
        padding: 10px;
        .markdown-content {
          line-height: 1.9;
          h1,h2,h3,h4,h5,h6,li,p{
            line-height: 1.9;
          }
        }

        .message-item {
          margin-bottom: 12px;

          .message-meta {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 6px;

            .avatar {
              width: 20px;
              height: 20px;
              border-radius: 50%;
              display: inline-flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              font-size: 12px;
              &.green { background: #4caf50; }
              &.orange { background: #ff9800; }
              &.blue { background: #1890ff; }
            }

            .name {
              color: #666;
              font-size: 12px;
            }

            .time {
              color: #999;
              font-size: 12px;
            }
          }

          .message-bubble {
            background: #f5f7fb;
            border: 1px solid #e8eef6;
            color: #333;
            border-radius: 8px;
            padding: 10px 12px;
          }
        }
      }
    }
  }
  .info-card {
      border-radius: 8px;
      // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      width: 80vw;
      margin: 0 auto;
      
      // &:hover {
      //   box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
      //   transform: translateY(-2px);
      // }

      .ant-card-head {
        border-bottom: 1px solid #f0f0f0;
        
        .ant-card-head-title {
          font-weight: 600;
          font-size: 14px;
          color: #262626;
        }
      }

      // 会议使用说明样式
      .meeting-instructions {
        .instruction-item {
          display: flex;
          align-items: flex-start;
          margin-bottom: 12px;
          
          &:last-child {
            margin-bottom: 0;
          }

          .step-number {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 20px;
            height: 20px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: 600;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
          }

          span:last-child {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
          }
        }
      }

      // 登录信息样式
      .login-info {
        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 0;
          border-bottom: 1px solid #f5f5f5;
          
          &:last-child {
            border-bottom: none;
          }

          .info-label {
            color: #666;
            font-size: 14px;
            font-weight: 500;
          }

          .info-value {
            color: #333;
            font-size: 14px;
            font-weight: 600;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
          }
        }
      }
      .record-files-section {
        margin-top: 20px;
      }

      // 简历分析和面试题目样式
      &.resume-card,
      &.questions-card {
        .ant-card-body {
          max-height: 300px;
          overflow-y: auto;
        }
      }

      .resume-analysis,
      .interview-questions {
        padding: 30px;
        line-height: 1.6;
        color: #333;
        font-size: 14px;
        
        // 自定义滚动条
        &::-webkit-scrollbar {
          width: 6px;
        }
        
        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }
        
        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;
          
          &:hover {
            background: #a8a8a8;
          }
        }
      }
  }
}

