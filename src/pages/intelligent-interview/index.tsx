// DraftPage.tsx
import React, {
  useState,
  useMemo,
  Suspense,
  useRef,
  createRef,
  RefObject,
  useEffect,
} from 'react'
import { Button, Flex, Steps, message, Spin } from 'antd'
import { getToken, getUserInfo } from '@/utils/auth'
import { cacheGet } from '@/utils/cacheUtil'
import useSSEChat from '@/hooks/useSSEChat'

import HeaderCom from '@/component/Header'
import MentionsComponent from '@/businessComponents/MentionsFileModule'
// import LeftSidebarModule from '@/businessComponents/LeftSidebarModule'
import type { MentionsComponentRef } from '@/businessComponents/MentionsFileModule'
import ReviewResult from '@/component/ReviewResult'
// import { intelligentInterview } from '@/api/intelligentInterview'

import './index.less'
// 输入输出评审agentId
const reviewAgentId = {
  场景输入官: '5aa01041-7e2b-4221-ab8d-a11d8db3ca8e',
  场景输出官: 'a40e5a31-da0a-4b70-91c3-1201baebfc9b',
  单元输入官: '1f72ff5f-d6b8-48e7-90ad-294cf58ab136',
  单元输出官: 'd00b1144-ad49-4c60-ac49-1cd16cf4bfeb',
  场景规划师: '871bc0ed-4d86-4b87-aa3c-2bc187e5da25',
}
const VITE_SCENE_INPUT: string = import.meta.env['VITE_SCENE_INPUT'] || '' // 场景输入官
const VITE_SCENE_OUTPUT: string = import.meta.env['VITE_SCENE_OUTPUT'] || '' // 场景输出官
const VITE_UNIT_INPUT: string = import.meta.env['VITE_UNIT_INPUT'] || '' // 单元场景输入评审
const VITE_UNIT_OUTPUT: string = import.meta.env['VITE_UNIT_OUTPUT'] || '' // 单元场景输出评审
const VITE_SCENE_PLANNING: string = import.meta.env['VITE_SCENE_PLANNING'] || '' // 场景规划师

const DraftPage: React.FC = () => {
  const sseChat = useSSEChat()
  // const sseChat2 = useSSEChat()
  const [current, setCurrent] = useState(-1)
  const [started, setStarted] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState('1')
  const [sidebarVisible, setSidebarVisible] = useState(false)
  const [uploadedFileData, setUploadedFileData] = useState<any>(null) // 新增：保存上传的文件数据
  // 步骤数据状态，包括各步骤的结果和开始数据
  const [stepData, setStepData] = useState<any>({}) // 所有步骤的数据. outputReview 输出评审是否通过  inputReview 输入评审是否通过
  // const [stepData2, setStepData2] = useState<any>({})

  // 全局加载状态管理
  const [globalLoading, setGlobalLoading] = useState(false)
  const [reviewLoading, setReviewLoading] = useState(false)

  //场景输入开始
  const [sceneReviewShow, setSceneReviewShow] = useState(false)
  const [sceneReviewContent, setSceneReviewContent] = useState<any>(null)
  const [reviewResult, setReviewResult] = useState<any>(null)
  const [scenarioSteps, setScenarioSteps] = useState<any[]>([])
  const [pendingSubmit, setPendingSubmit] = useState<number | null>(null) // 新增：待执行的提交操作

  const [leftTabs, setLeftTabs] = useState<any[]>([
    {
      title: '简历预览',
      content: 'ResumePreview',
      key: 'resume-preview',
      props: {
        // stepIndex: 0,
        viewData: null, // 传递文件数据
      },
    },
    {
      title: '简历解析',
      content: 'ResumeAnalysis',
      key: 'basicAnalysisResult',
      props: {
        viewData: null, // 传递数据
      },
    },
    {
      title: '岗位匹配',
      content: 'JobMatching',
      key: 'jobMatchResult',
      props: {
        viewData: null, // 传递数据
      },
    },
    {
      title: '智能题目',
      content: 'QuestionGeneration',
      key: 'questionResult',
      props: {
        viewData: null, // 传递数据
      },
    },
  ])
  const [sessionId, setSessionId] = useState<string>('')
  const [candidateName, setCandidateName] = useState<string>('')
  const [pageInfo, setPageInfo] = useState<any>({
    pageName: '智能面试助手',
    pageDesc: '上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    pageInputDesc:
      '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    pageOutputDesc:
      '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
    steps: [
      {
        title: '简历解析',
        content: '<ResumeAnalysisModule />',
        agentId: 'ea543945-0d56-42a3-98bb-f06a3f4dd2f6',
        currentEchoData: {},
      },
      {
        title: '智能出题',
        content: '<IntelligentQuestionModule />',
        agentId: '54331e66-df8b-48f3-b918-736b57f59d64',
        currentEchoData: {},
      },
      {
        title: '面试安排',
        content: '<InterviewArrangementModule />',
        agentId: '',
        currentEchoData: {},
      },
      {
        title: '视频面试',
        content: '<VideoInterviewModule />',
        agentId: '9c8f1695-4gee-673g-d685-2f8adcg92ch',
        currentEchoData: {},
      },
      {
        title: '面试转录',
        content: '<TranscriptModule />',
        agentId: '9c8f1695-4gee-673g-d685-2f8adcg92ch',
        currentEchoData: {},
      },
      {
        title: '评估报告',
        content: '<EvaluationReportModule />',
        agentId: '7bdd055f-bcc9-4f89-ba23-a2668b1196f2',
        currentEchoData: {},
      },
    ],
  })
  const [type, setType] = useState('scene')
  //取 pageInfo 自己的基本信息 + steps 里某一步
  const getStepData = (pageInfo: any, stepIndex: number) => {
    const { steps, ...pageBase } = pageInfo // 把 steps 拆出来，剩下的是 pageInfo 自身信息
    const step = steps[stepIndex] // 拿到某一步

    return {
      ...pageBase,
      step, // 保留单步
    }
  }
  const isOutputReview = useRef(false) // 看是输入评审还是输出评审

  const isSceneInputReview = useRef(true) // 场景输入为true 场景输出为false

  const [outputReviewName, setOutputReviewName] = useState('场景输入评审') // 看是场景评审还是单元评审
  const [isUnitReview, setIsUnitReview] = useState(false) //是否单元评审

  // 将pageInfo移到组件内部，使其能够访问uploadedFileData状态
  // const pageInfo = {
  //   pageName: '智能面试助手',
  //   pageDesc: '上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
  //   pageInputDesc:
  //     '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
  //   pageOutputDesc:
  //     '请上传简历，相关信息，简历解析，智能出题，在线面试，面试总结',
  //   steps: [
  //     {
  //         title: "简历解析",
  //         content: "<ResumeAnalysisModule />",
  //         submitName: "解析简历",
  //         isExport: false,
  //         agentId: "ea543945-0d56-42a3-98bb-f06a3f4dd2f6",
  //         inputDesc: "请上传简历及相关信息，用于系统解析简历内容并提取关键信息。",
  //         outputDesc: "系统将输出解析后的简历内容，包括基本信息、工作经历、教育背景等关键信息。",
  //         inputSource: "用户上传的简历及相关信息。",
  //         currentEchoData: {}

  //     },
  //     {
  //         title: "智能出题",
  //         content: "<IntelligentQuestionModule />",
  //         submitName: "生成题目",
  //         isExport: false,
  //         agentId: "54331e66-df8b-48f3-b918-736b57f59d64",
  //         inputDesc: "根据用户上传的简历及岗位信息，输入用于智能出题的岗位需求及候选人信息",
  //         outputDesc: "输出针对该岗位和候选人的智能生成的面试题目",
  //         inputSource: "简历解析结果和岗位相关信息",
  //         currentEchoData: {}
  //     },
  //     {
  //         title: "面试安排",
  //         content: "<InterviewArrangementModule />",
  //         submitName: "提交安排",
  //         isExport: false,
  //         agentId: "",
  //         inputDesc: "用户需提供面试安排相关信息，例如面试时间、面试岗位、参与面试人员等。",
  //         outputDesc: "系统将根据输入信息生成面试安排详情，并展示后续操作入口。",
  //         inputSource: "用户手动输入或上传的面试安排数据。",
  //         currentEchoData: {}
  //     },
  //     {
  //         title: "视频面试",
  //         content: "<VideoInterviewModule />",
  //         submitName: "开始视频面试",
  //         isExport: false,
  //         agentId: "9c8f1695-4gee-673g-d685-2f8adcg92ch",
  //         inputDesc: "进入视频面试环节，系统将根据简历解析和智能出题的结果进行在线面试。",
  //         outputDesc: "完成视频面试后，系统将生成面试记录并用于后续的面试总结。",
  //         inputSource: "简历解析、智能出题的结果，以及面试官或系统设置的面试流程。",
  //         currentEchoData: {}
  //     },
  //     {
  //         title: "面试转录",
  //         content: "<TranscriptModule />",
  //         submitName: "开始转录",
  //         isExport: false,
  //         agentId: "9c8f1695-4gee-673g-d685-2f8adcg92ch",
  //         inputDesc: "用户提供面试相关的音频或视频资料，用于进行转录处理。",
  //         outputDesc: "系统将返回转录后的文本内容，便于后续分析和使用。",
  //         inputSource: "用户上传的音频或视频文件，来源于在线面试或录制的面试过程。",
  //         currentEchoData: {}
  //     },
  //     {
  //         title: "评估报告",
  //         content: "<EvaluationReportModule />",
  //         submitName: "生成评估报告",
  //         isExport: false,
  //         agentId: "7bdd055f-bcc9-4f89-ba23-a2668b1196f2",
  //         inputDesc: "上传简历及相关信息后，系统将根据内容生成评估报告",
  //         outputDesc: "输出包含简历解析结果、岗位匹配度及面试建议的评估报告",
  //         inputSource: "用户上传的简历及相关信息",
  //         currentEchoData: {}
  //     }
  // ],
  // }

  const currentStep = pageInfo.steps[current]
  const mentionsRef = useRef<MentionsComponentRef>(null)

  // 自动匹配业务组件
  const modules = import.meta.glob('/src/businessComponents/**/index.tsx')

  // 所有组件的 ref 映射
  const refs = useRef<Record<number, RefObject<any>>>({})

  // 懒加载当前组件
  const DynamicComponent = useMemo(() => {
    if (current < 0) return
    const match = currentStep.content.match(/<(\w+)\s*\/>/)
    const componentName = match?.[1]
    console.log('当前组件名称:', componentName)

    if (!componentName) return null

    const modulePath = `/src/businessComponents/${componentName}/index.tsx`
    const loader = modules[modulePath]

    if (!loader) {
      console.warn('模块未找到:', modulePath)
      return null
    }

    // 创建 ref（如果不存在）
    if (!refs.current[current]) {
      refs.current[current] = createRef()
    }

    const LazyComponent = React.lazy(loader as any)

    // 返回一个函数，该函数接收props并渲染组件
    return (props: any) => (
      <LazyComponent ref={refs.current[current]} {...props} />
    )
  }, [current])

  // 根据stepData 更新 leftTabs 的 props
  useEffect(() => {
    console.log(stepData, '--------stepData')
    if (current == 0) {
      setLeftTabs((leftTabs) =>
        leftTabs.map((item: any) =>
          item.key === 'resume-preview'
            ? {
                ...item,
                props: {
                  ...item.props,
                  viewData: stepData.startData.localFile,
                },
              }
            : item
        )
      )
    } else if (current == 1) {
      let keys = ['basicAnalysisResult', 'jobMatchResult']
      const updatedUser = leftTabs.map((item) =>
        keys.includes(item.key)
          ? {
              ...item,
              props: { ...item.props, viewData: stepData[0][item.key] },
            }
          : item
      )
      setLeftTabs(updatedUser)
    } else if (current >= 2) {
      let keys = ['basicAnalysisResult', 'jobMatchResult', 'questionResult']
      const updatedUser = leftTabs.map((item) => {
        if (
          item.key === 'basicAnalysisResult' ||
          item.key === 'jobMatchResult'
        ) {
          return {
            ...item,
            props: { ...item.props, viewData: stepData[0]?.[item.key] },
          }
        } else if (item.key === 'questionResult') {
          return {
            ...item,
            props: { ...item.props, viewData: stepData[1]?.[item.key] },
          }
        }
        return item
      })
      setLeftTabs(updatedUser)
    }
    // else if(current == 5) {
    //   let keys = ['basicAnalysisResult','jobMatchResult','questionResult']
    //   const updatedUser = leftTabs.map(item => {
    //     if (item.key === 'basicAnalysisResult' || item.key === 'jobMatchResult') {
    //       return { ...item, props: { ...item.props, viewData: stepData[0]?.[item.key] }}
    //     } else if (item.key === 'questionResult') {
    //       return { ...item, props: { ...item.props, viewData: stepData[1]?.[item.key] }}
    //     }
    //     return item
    //   })
    //   setLeftTabs(updatedUser)
    // }
  }, [stepData, current])

  // 开始流程操作 - 获取开始数据并启动面试流程
  const onStartClick = async () => {
    let childData: any
    // 如果还没有开始，从mentionsRef获取数据
    // if (!started) {
    // 获取用户输入的文件和查询内容
    childData = mentionsRef.current?.getMentionsData?.()
    console.log('开始按钮点击，获取MentionsComponent数据:', childData)

    // 验证必要数据：必须有查询内容和上传的文件
    if (!childData?.query || !childData?.localFile?.length) {
      message.warning('请先输入内容并上传文件')
      return
    }

    // 先显示评审结果面板，场景规划生成后再在组件内呈现
    setSceneReviewShow(true)
    try {
      // 保存文件数据到状态中，供后续步骤使用
      setUploadedFileData(childData.localFile)
      // 保存开始数据到步骤状态中，供子组件使用
      setStepData((prev: any) => ({
        ...prev,
        startData: {
          query: childData.query,
          localFile: childData.localFile,
          timestamp: Date.now(),
        },
      }))
      setSceneReviewShow(true)
      // 场景的输入

      getReviewInfo(
        VITE_SCENE_INPUT,
        childData?.query,
        pageInfo,
        childData?.localFile
      )
      // // 创建会话 --先注释掉
      // let currentSessionId = sessionId;
      // if (!currentSessionId) {
      //   currentSessionId = await intelligentInterview.createSession(
      //     candidateName
      //   );
      //   console.log('创建会话成功:', currentSessionId);
      //   setSessionId(currentSessionId);
      // }
      // 记得打开场景输入评审
      // getReviewInfo(
      //   reviewAgentId['场景输入官'],
      //   childData.query,
      //   pageInfo,
      //   childData.localFile
      // )
    } catch (error) {
      console.error('启动面试流程失败:', error)
      // setGlobalLoading(false);
      message.error('启动面试流程失败，请重试')
    }
    // }
  }
  // 场景输入输出评审信息
  const getReviewInfo = async (
    agentId: string,
    inputQuery: string,
    pageInfo: any,
    uploadedFileData: any
  ) => {
    const fileData: any[] = []
    // debugger
    setGlobalLoading(true)
    setSceneReviewShow(true)
    uploadedFileData.forEach((item: any) => {
      fileData.push({
        type: item.fileType || 'document',
        transfer_method: 'local_file',
        upload_file_id: item.id,
      })
    })
    try {
      if (agentId) {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },

          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            query: inputQuery || '',
            difyJson: {
              inputs: {
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: inputQuery || '',
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || '',
          // onMessage: (res: any) => {
          //   console.log(res, '输入/输出评审内容')
          // },
          onFinished: (res) => {
            console.log(res, '输入/输出评审完成')
            // debugger
            if (!res) return false
            const jsonStr = extractJsonString(res)
            try {
              const parsed = JSON.parse(jsonStr)
              console.log(parsed, 'parsed')
              setSceneReviewContent(parsed)
              console.log('设置 sceneReviewContent:', parsed)
              setGlobalLoading(false)
              setReviewResult(parsed['评审结果'] == '正常')
              if (isSceneInputReview.current) {
                setOutputReviewName('场景输入评审')
                if (parsed['评审结果'] == '正常') {
                  // 开始场景规划
                  sceneReviewStartFn(fileData, inputQuery)
                }
              } else {
                setOutputReviewName('场景输出评审')
                setTimeout(() => {
                  setSceneReviewShow(false)
                }, 1000)
              }
              setType('scene')
            } catch (e) {
              console.error('输入/输出场景评审结果解析失败:', e)
            }
          },
        })
      }
    } catch (error) {
      setGlobalLoading(false)
    }
  }

  // 从字符串中安全提取 JSON：
  // 1) 去掉 <think>...</think>
  // 2) 优先提取 ```json ``` 代码块内容
  // 3) 回退：截取第一个 '{' 到最后一个 '}' 之间的内容
  const extractJsonString = (raw: string): string => {
    if (!raw) return ''
    const noThink = raw.replace(/<think>[\s\S]*?<\/think>/g, '').trim()
    const fenced = noThink.match(/```(?:json)?\s*([\s\S]*?)```/i)
    if (fenced && fenced[1]) {
      return fenced[1].trim()
    }
    const first = noThink.indexOf('{')
    const last = noThink.lastIndexOf('}')
    if (first !== -1 && last !== -1 && last > first) {
      return noThink.slice(first, last + 1).trim()
    }
    return noThink
  }
  // 轮询调用场景规划
  const sceneReviewStartFn = async (fileData: any, inputQuery: any) => {
    setGlobalLoading(true)
    // setSceneReviewContent(null)
    // setSceneReviewStart(true)
    const tokenInfo: any = await getToken()
    const userInfo: any = await getUserInfo()
    const tenantId: any = cacheGet('tenantId')
    const ctx = { tokenInfo, tenantId, userId: userInfo?.id || 'anonymous' }

    const result: any[] = []
    // 循环调用每个步骤，一个完成后再调用下一个
    for (const item of pageInfo.steps) {
      console.log(`开始处理步骤: ${item.title}`)
      const stepResult: any = await fetchDataById(
        {
          pageName: pageInfo.pageName,
          pageDesc: pageInfo.pageDesc,
          pageInputDesc: pageInfo.pageInputDesc,
          pageOutputDesc: pageInfo.pageOutputDesc,
          steps: [item],
        },
        ctx,
        fileData,
        inputQuery
      )
      const jsonStr = extractJsonString(stepResult)
      console.log(jsonStr, 'jsonStr')
      try {
        if (typeof jsonStr == 'string') {
          console.log('json字符串', jsonStr)
          // 是 JSON 字符串，需要解析
          result.push(JSON.parse(jsonStr))
        } else if (typeof jsonStr == 'object') {
          console.log('json对象', jsonStr)
          // 是 JSON 对象，可以直接使用
          result.push(jsonStr)
        } else {
          console.error('解析 JSON 失败: ', jsonStr)
        }
        // result.push(JSON.parse(jsonStr))
      } catch (e) {
        console.error('解析 JSON 失败: ', e, jsonStr)
      }
      // console.log(`步骤 ${item.title} 处理完成`)
    }

    console.log(result, '场景规划结果')

    // 1) 融合：将规划结果与原始 step 元数据合并（按顺序一一对应）
    const merged = pageInfo.steps.map((step: any, idx: any) => ({
      ...step,
      ...(result[idx] || {}),
    }))
    console.log(merged, 'merged')
    setScenarioSteps(merged)
    setGlobalLoading(false)
  }
  const fetchDataById = async (
    data: any,
    ctx: any,
    fileData: any,
    inputQuery: any
  ) => {
    const agentId = reviewAgentId['场景规划师']
    return new Promise((resolve) => {
      sseChat.start({
        url: '/dify/broker/agent/stream',
        headers: {
          'Content-Type': 'application/json',
          Token: ctx.tokenInfo || '',
        },

        body: {
          insId: '1',
          bizType: 'app:agent',
          bizId: agentId,
          agentId: agentId,
          path: '/chat-messages',
          query: inputQuery || '',
          difyJson: {
            inputs: {
              Token: ctx.tokenInfo || '',
              tenantid: ctx.tenantId || '',
              pageInfo: JSON.stringify(data),
            },
            response_mode: 'streaming',
            user: ctx.userId || 'anonymous',
            conversation_id: '',
            query: inputQuery || '',
            files: fileData,
            // pageInfo: JSON.stringify(pageInfo),
          },
        },
        query: {},
        message: inputQuery || '',
        onMessage: (res: any) => {
          // console.log(res, '输入/输出评审内容')
        },
        onFinished: (res) => {
          console.log(res, '场景规划-----')
          resolve(res)
        },
      })
    })
    console.log(data)
  }
  // 确认无误后，开始任务规划
  const confirmScenario = (steps: any) => {
    console.log(steps, '最新steps数据')
    // 融合数据（场景规划可能会改动steps 所以需要再set一下）
    setPageInfo((prev: any) => ({
      ...prev,
      steps: steps,
    }))
    setType('unit')
    setSceneReviewShow(false)
    setCurrent(0)
    setIsUnitReview(true)

    // 延迟调用 getUnitReviewInfo，确保组件有足够时间渲染
    setTimeout(() => {
      const obj = getStepData(pageInfo, 0)
      // 单元输入评审
      getUnitReviewInfo(
        0,
        false,
        VITE_UNIT_INPUT,
        stepData?.startData?.query,
        obj,
        stepData?.startData?.localFile
      )
    }, 100) // 100ms 延迟
  }
  // 单元场景的评审开始
  // 输入输出评审单元的
  const getUnitReviewInfo = async (
    targetCurrent: number,
    isOutput: boolean,
    agentId: string,
    inputQuery?: string,
    pageInfo?: any,
    uploadedFileData?: any,
    childData?: any
  ) => {
    const fileData: any[] = []
    setGlobalLoading(true)
    // setSceneReviewContent(null)
    if (uploadedFileData && uploadedFileData.length > 0) {
      uploadedFileData?.forEach((item: any) => {
        fileData.push({
          type: item.fileType || 'document',
          transfer_method: 'local_file',
          upload_file_id: item.id,
        })
      })
    }
    isOutputReview.current = isOutput // 看当前是输入还是输出评审
    try {
      if (agentId) {
        const tokenInfo = await getToken()
        const userInfo = await getUserInfo()
        const tenantId = cacheGet('tenantId')
        sseChat.start({
          url: '/dify/broker/agent/stream',
          headers: {
            'Content-Type': 'application/json',
            Token: tokenInfo || '',
          },

          body: {
            insId: '1',
            bizType: 'app:agent',
            bizId: agentId,
            agentId: agentId,
            path: '/chat-messages',
            query: inputQuery || '',
            difyJson: {
              inputs: {
                Token: tokenInfo || '',
                tenantid: tenantId || '',
                pageInfo: JSON.stringify(pageInfo),
              },
              response_mode: 'streaming',
              user: userInfo?.id || 'anonymous',
              conversation_id: '',
              query: inputQuery || '',
              files: fileData,
              // pageInfo: JSON.stringify(pageInfo),
            },
          },
          query: {},
          message: inputQuery || '',
          // onMessage: (res: any) => {
          //   console.log(res, '输入/输出评审内容')
          // },
          onFinished: (res) => {
            console.log(res, '单元输入/输出评审onFinished')
            if (!res) return false
            const jsonStr = extractJsonString(res)
            try {
              const parsed = JSON.parse(jsonStr)
              setSceneReviewContent(parsed)
              console.log('设置 sceneReviewContent (单元评审):', parsed)
              setReviewResult(parsed['评审结果'] == '正常')
              setGlobalLoading(false)
              setIsUnitReview(false)
              if (isOutput) {
                // 说明是单元输出评审
                setStepData((prev: any) => ({
                  ...prev,
                  [targetCurrent]: {
                    ...prev[targetCurrent],
                    outputReview: true,
                  },
                }))
                if (targetCurrent == 5) {
                  // 说明是最后一步输出评审，场景输出评审
                  isSceneInputReview.current = false
                  const ouputData = `${stepData[0]?.resumeAnalysisResult}, ${childData?.reportContent}`
                  getReviewInfo(
                    VITE_SCENE_OUTPUT,
                    ouputData,
                    pageInfo,
                    stepData?.startData?.localFile
                  )
                }
              } else {
                // 说明是单元输入评审
                getSubmit(targetCurrent)
              }
            } catch (e) {
              console.error('输入/输出评审结果解析失败:', e, jsonStr)
            }
          },
        })
      }
    } catch (error) {
      setGlobalLoading(false)
    }
  }
  // 输入评审完毕后，调用各个单元的提交.  重新生成
  const getSubmit = (targetCurrent: number) => {
    console.log(refs.current, 'refs.current')
    console.log(targetCurrent, 'targetCurrent')
    const ref = refs.current[targetCurrent]
    if (targetCurrent == 0) {
      ref?.current?.handleAutoStartResumeAnalysis?.()
    } else if (targetCurrent == 1) {
      ref?.current?.handleAutoStart?.()
    } else if (targetCurrent == 2) {
      ref?.current?.handleCreateMeeting?.()
    } else if (targetCurrent == 3) {
      // ref?.current?.handleAutoStartResumeAnalysis?.()
      // ref?.current?.handlers[targetCurrent]?.();
    } else if (targetCurrent == 4) {
      ref?.current?.handleStartTranscript?.()
    } else if (targetCurrent == 5) {
      ref?.current?.handleAutoStart?.()
    }
  }
  // 下一步
  const getNextStep = async (item: any) => {
    const ref = refs.current[current]
    const childData = ref?.current?.getMentionsData?.() // 获取子组件暴露方法
    console.log('提交数据:', childData)
    // setStepData((prev: any) => ({
    //   ...prev,
    //   [current]: childData,
    // }))
    setStepData((prev: any) => ({
      ...prev,
      [current]: {
        ...prev[current],
        ...childData,
      },
    }))
    if (current < pageInfo.steps.length - 1) {
      setCurrent(current + 1)
      setIsUnitReview(true)
      if (!stepData[current + 1]?.outputReview) {
        const obj = getStepData(pageInfo, current)
        if (current == 0) {
          getUnitReviewInfo(
            current + 1,
            false,
            VITE_UNIT_INPUT,
            childData.info,
            obj
          )
        } else if (current == 4) {
          let inputData = `${stepData[0]?.resumeAnalysisResult},${childData?.finalTranscript}`
          getUnitReviewInfo(current + 1, false, VITE_UNIT_INPUT, inputData, obj)
        }
      }
    }
  }
  // 提交操作
  const getSubmitInfo = async (item: any) => {
    const ref = refs.current[current]
    const childData = ref?.current?.getMentionsData?.() // 获取子组件暴露方法
    console.log('提交数据:', childData)
    // if (item?.agentId) {
    //   const tokenInfo = await getToken();
    //   const userInfo = await getUserInfo();
    //   const tenantId = cacheGet("tenantId");

    //   const fileData: any[] = [];
    //   childData?.localFile?.forEach((item: any) => {
    //     fileData.push({
    //       type: item.fileType || "document",
    //       transfer_method: "local_file",
    //       upload_file_id: item.id,
    //     });
    //   });

    //   sseChat.start({
    //     url: "/dify/broker/agent/stream",
    //     headers: {
    //       "Content-Type": "application/json",
    //       Token: tokenInfo || "",
    //     },
    //     body: {
    //       insId: "1",
    //       bizType: "app:agent",
    //       bizId: item.agentId,
    //       agentId: item.agentId,
    //       path: "/chat-messages",
    //       query: childData?.query || "",
    //       difyJson: {
    //         inputs: {
    //           docFiles: fileData,
    //           Token: tokenInfo || "",
    //           tenantid: tenantId || "",
    //           outputTemplate: null, // templateFile 可根据实际情况补上
    //         },
    //         response_mode: "streaming",
    //         user: userInfo?.id || "anonymous",
    //         conversation_id: "",
    //         query: childData?.query || "",
    //       },
    //     },
    //     query: {},
    //     message: childData?.query || "",
    //     onFinished: () => {
    //       if (current < pageInfo.steps.length - 1) {
    //         setCurrent(current + 1);
    //       }
    //     },
    //   });
    // } else {
    // 存到父组件 state
    setStepData((prev: any) => ({
      ...prev,
      [current]: childData,
    }))
    if (current < pageInfo.steps.length - 1) {
      setCurrent(current + 1)
    }
    // }
  }

  // 下载报告
  const onDownloadReport = () => {
    console.log('下载报告')
    const ref = refs.current[current]
    const childData = ref?.current?.getMentionsData?.() // 获取子组件暴露方法
    setStepData((prev: any) => ({
      ...prev,
      [current]: childData,
    }))
    console.log(childData, 'childData')
    const reportContent = childData?.reportContent
    if (!reportContent) {
      message.error('报告内容为空')
      return
    }

    try {
      const blob = new Blob([reportContent], { type: 'text/markdown' })
      const url = URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `面试评估报告_${sessionId}_${
        new Date().toISOString().split('T')[0]
      }.md`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      message.success('报告下载成功！')
    } catch (error) {
      console.error('下载失败:', error)
      message.error('下载失败，请稍后重试')
    }
  }
  // 子组件调用输出评审（子组件传过来的）
  // stepCurrent 第几步
  // stepCurrentData 第几步的数据
  const handleFromChild = (type: string, stepCurrentData: any) => {
    const obj = getStepData(pageInfo, current)
    let isRtye = false
    setIsUnitReview(true)
    if (type == '输出') {
      isRtye = true
      const ref = refs.current[current]
      const childData = ref?.current?.getMentionsData?.() // 获取子组件暴露方法
      // 将子组件暴露出来的数据存到父组件
      setStepData((prev: any) => ({
        ...prev,
        [current]: {
          ...prev[current],
          ...childData,
        },
      }))
      getUnitReviewInfo(
        current,
        isRtye,
        VITE_UNIT_OUTPUT,
        stepCurrentData,
        obj,
        childData
      )
    } else if (type == '输入') {
      isRtye = false
      getUnitReviewInfo(current, isRtye, VITE_UNIT_INPUT, stepCurrentData, obj)
    }
  }
  // 忽略错误/确定
  const continueScenario = () => {
    if (isSceneInputReview.current) {
      let fileData = uploadedFileData.map((item: any) => {
        return {
          type: item.fileType || 'document',
          transfer_method: 'local_file',
          upload_file_id: item.id,
        }
      })

      setGlobalLoading(true)
      sceneReviewStartFn(fileData, stepData?.startData?.query)
    } else {
      setSceneReviewShow(false)
    }
  }
  // 关闭场景输出评审
  const onClose = () => {
    setSceneReviewShow(false)
    setType('unit')
  }
  return (
    <Flex vertical className="interview-page" align="center">
      <Spin tip="加载中" spinning={globalLoading} fullscreen size="large" />
      {/* 左侧边栏 */}
      {/* <LeftSidebarModule 
        sessionId={currentSessionId}
        onSessionSelect={handleSessionSelect}
        onSidebarToggle={handleSidebarToggle}
      /> */}
      <Flex
        className={`main-content ${sidebarVisible ? 'sidebar-open' : ''}`}
        vertical
        justify="center"
      >
        <Flex vertical className="interview-page-con" justify="center">
          {current < 0 && (
            <>
              <HeaderCom
                mainTitle={pageInfo.pageName}
                subTitle={pageInfo.pageDesc}
              ></HeaderCom>
              {!sceneReviewShow && (
                <div style={{ width: '60vw', margin: '0 auto' }}>
                  <MentionsComponent
                    // agentId="0f31b4cb-5752-409d-b47d-38a9c1e162d6"
                    agentId="ea543945-0d56-42a3-98bb-f06a3f4dd2f6"
                    ref={mentionsRef}
                  />
                  <div
                    className="start-btn"
                    style={{ width: '100%', textAlign: 'center' }}
                  >
                    <Button
                      type="primary"
                      block
                      onClick={() => {
                        onStartClick()
                      }}
                      style={{ marginRight: 8 }}
                    >
                      开始
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
          {current >= 0 && (
            <>
              <div className="step-header">
                <div className="step-header-left">
                  <p className="step-left-title">智能面试场景</p>
                  <p className="step-left-desc">
                    简历解析，智能出题，在线面试，面试总结
                  </p>
                </div>
                <Steps
                  current={current}
                  style={{ marginBottom: 24 }}
                  className="custome-steps"
                  // onChange={onChange}
                >
                  {pageInfo.steps.map((item: any) => (
                    <Steps.Step key={item.title} title={item.title} />
                  ))}
                </Steps>
              </div>
              <div style={{ display: sceneReviewShow ? 'none' : 'block' }}>
                <Flex
                  vertical
                  justify="center"
                  className="step-component-content"
                >
                  <Suspense fallback={<div>加载中...</div>}>
                    {DynamicComponent ? (
                      <DynamicComponent
                        setGlobalLoading={setGlobalLoading}
                        globalLoading={globalLoading}
                        leftTabs={leftTabs}
                        // currentStep={currentStep.title}
                        stepIndex={current}
                        fileData={uploadedFileData} // 传递文件数据
                        startData={stepData.startData} // 传递简历解析数据（查询内容、文件等）
                        questionData={{ queryData: stepData[0] }} // 传递智能出题数据
                        meetingScheduleData={{
                          candidateName:
                            stepData[0]?.basicAnalysisResult?.basic_info?.name,
                        }} // 传递面试安排数据
                        videoInterviewData={stepData[2]?.meetingData} // 传递视频面试数据
                        agentId={currentStep.agentId} // 传递当前步骤的agentId
                        currentEchoData={
                          stepData[current] ? stepData[current] : {}
                        } // 传递当前步骤的回显数据
                        reportData={{
                          resumeAnalysisResult: stepData[0]
                            ? stepData[0].resumeAnalysisResult
                            : '',
                          transcript_md: stepData[4]?.finalTranscript,
                        }}
                        pageInfo={{
                          pageName: pageInfo.pageName,
                          pageDesc: pageInfo.pageDesc,
                          pageInputDesc: pageInfo.pageInputDesc,
                          pageOutputDesc: pageInfo.pageOutputDesc,
                          steps: [currentStep],
                        }}
                        onCallParent={handleFromChild} // 子组件调用父组件的方法 进行单元场景输出处理
                      />
                    ) : (
                      <div>组件未找到</div>
                    )}
                  </Suspense>
                </Flex>
                <div style={{ textAlign: 'center', position: 'relative' }}>
                  {current > 0 && (
                    <Button
                      onClick={() => setCurrent(current - 1)}
                      style={{ marginRight: 8 }}
                    >
                      上一步
                    </Button>
                  )}
                  {current < pageInfo.steps.length - 1 && (
                    <Button
                      type="primary"
                      onClick={() => getNextStep(currentStep)}
                    >
                      下一步
                    </Button>
                  )}
                  {/* {currentStep.isExport && (
                <Button type="primary" onClick={() => console.log("导出操作")}>
                  导出
                </Button>
              )} */}
                  {current == pageInfo.steps.length - 1 && (
                    <Button onClick={getSubmitInfo} style={{ marginRight: 8 }}>
                      重新开始
                    </Button>
                  )}

                  {current == pageInfo.steps.length - 1 && (
                    <Button
                      type="primary"
                      onClick={getSubmitInfo}
                      style={{ marginRight: 8 }}
                    >
                      完成面试
                    </Button>
                  )}

                  {current == pageInfo.steps.length - 1 && (
                    <Button type="primary" onClick={onDownloadReport}>
                      下载报告
                    </Button>
                  )}
                  {globalLoading && type === 'unit' && isUnitReview && (
                    <div
                      style={{
                        position: 'absolute',
                        right: '50px',
                        bottom: '0',
                        lineHeight: '32px',
                        color: '#1888ff',
                      }}
                    >
                      {isOutputReview.current ? '输出评审中' : '输入评审中'}
                    </div>
                  )}
                </div>
              </div>
            </>
          )}
          {/* 场景评审/单元评审 */}
          {sceneReviewShow && (
            <ReviewResult
              type={type}
              title={outputReviewName}
              steps={scenarioSteps}
              reviewContent={sceneReviewContent}
              reviewResult={reviewResult}
              showScenario={isSceneInputReview.current && scenarioSteps.length > 0 }
              onBackIndex={() => {
                setSceneReviewShow(false)
              }}
              onContinue={continueScenario}
              onClose={onClose}
              onConfirmScenario={(steps) => confirmScenario(steps)}
            />
          )}
        </Flex>
      </Flex>
    </Flex>
  )
}

export default DraftPage
